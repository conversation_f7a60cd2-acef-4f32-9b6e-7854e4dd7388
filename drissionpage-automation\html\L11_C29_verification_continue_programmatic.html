<!DOCTYPE html><html lang="en"><head><meta http-equiv="origin-trial" content="A7vZI3v+Gz7JfuRolKNM4Aff6zaGuT7X0mf3wtoZTnKv6497cVMnhy03KDqX7kBz/q/iidW7srW31oQbBt4VhgoAAACUeyJvcmlnaW4iOiJodHRwczovL3d3dy5nb29nbGUuY29tOjQ0MyIsImZlYXR1cmUiOiJEaXNhYmxlVGhpcmRQYXJ0eVN0b3JhZ2VQYXJ0aXRpb25pbmczIiwiZXhwaXJ5IjoxNzU3OTgwODAwLCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ==">
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">

<link href="https://fonts.googleapis.com/css?family=Offside" rel="stylesheet" type="text/css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@500&amp;family=Roboto&amp;display=swap" rel="stylesheet">

<meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Augment Verification</title>
    <link rel="icon" href="/static/favicon.ico">
    <style type="text/css">
      :root{--augment-light-blue:#2f92db;--augment-dark-blue:#242e4e;--augment-green:#3d855e;--augment-green-dark:#114e0b;--augment-grey:#736d63;--augment-grey-light:#f5f5f5;--page-background:#fff;--text-white-80:#fffc;--system-fonts:-apple-system,BlinkMacSystemFont,avenir next,avenir,segoe ui,helvetica neue,helvetica,Cantarell,Ubuntu,roboto,noto,arial,sans-serif;--p-2:0.5rem;--p-3:0.75rem;--p-4:1rem;--p-8:2rem;--p-14:3.5rem;--gap-2:0.5rem;--gap-6:1.5rem;--text-sm-size:0.875rem;--text-sm-lh:1.25rem;--text-base-size:1rem;--text-lg-size:1.125rem;--text-3xl-size:1.875rem;--text-5xl-size:3rem}*{box-sizing:border-box;-webkit-font-smoothing:antialiased}body{background:var(--page-background);font-family:var(--system-fonts);font-size:var(--text-base-size);margin:0;padding:0}a{color:var(--augment-green);text-decoration:none}.l-oauth{background:var(--augment-grey-light);box-sizing:border-box;display:grid;font-weight:500;grid-auto-rows:1fr auto;height:100dvh}.l-oauth__center{text-align:center}.l-oauth__center,.l-oauth__left{background:#fff;border-radius:24px;display:flex;flex-direction:column;gap:var(--p-4);margin:var(--p-4);max-width:640px;place-self:center}.l-oauth__left{text-align:left}.l-oauth__content{padding:24px}@media (min-width:700px){.l-oauth__content{padding:48px 108px 62px}}.c-header{align-items:baseline;display:grid;grid-template-columns:auto auto;grid-template-rows:auto auto;justify-content:center;margin-bottom:20px}.c-header__welcome{color:var(--augment-green);font-family:Offside;font-size:var(--text-lg-size);font-weight:400;grid-column:1/3;margin-left:48px;opacity:0;text-align:left}.c-header__title{color:var(--augment-green-dark);font-family:Offside;font-size:var(--text-3xl-size);font-weight:400;margin:0;position:relative;top:-.18rem}.c-header__logo svg{height:48px;width:auto}.c-header .c-augi-logo__smile,.c-header:hover .c-augi-logo__default{opacity:0}.c-header:hover .c-augi-logo__smile,.c-header:hover .c-header__welcome{opacity:1}@media (min-width:700px){.c-header{margin-bottom:34px}.c-header__title{font-size:var(--text-5xl-size);position:relative;top:-.18rem}.c-header__logo svg{height:62px}}.c-footer{align-items:center;background-color:var(--augment-green);color:var(--text-white-80);display:flex;flex-direction:column;font-family:"Inter",var(--system-fonts);font-size:var(--text-sm-size);gap:var(--gap-6);justify-content:space-between;line-height:var(--text-sm-lh);padding:var(--p-2) var(--p-4);text-align:center}.c-footer p{margin:var(--p-4) 0}.c-footer a{color:var(--text-white-80);font-size:inherit;padding:var(--p-3)}.c-footer__links{align-items:center;display:flex;flex-direction:column;gap:var(--gap-2)}@media (min-width:540px){.c-footer,.c-footer__links{flex-direction:row}}.u-text-sm{font-size:var(--text-sm-size);line-height:var(--text-sm-lh)}.gsi-material-button{-webkit-appearance:none;background-color:#f2f2f2;background-image:none;border:none;border-radius:20px;box-sizing:border-box;color:#1f1f1f;cursor:pointer;font-family:Roboto,arial,sans-serif;font-size:1rem;height:40px;letter-spacing:.25px;max-width:400px;min-width:-moz-min-content;min-width:min-content;outline:none;overflow:hidden;padding:0 12px;position:relative;text-align:center;transition:background-color .218s,border-color .218s,box-shadow .218s;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;vertical-align:middle;white-space:nowrap;width:auto}.gsi-material-button .gsi-material-button-icon{height:20px;margin-right:12px;min-width:20px;width:20px}.gsi-material-button .gsi-material-button-content-wrapper{align-items:center;display:flex;flex-direction:row;flex-wrap:nowrap;height:100%;justify-content:space-between;position:relative;width:100%}.gsi-material-button .gsi-material-button-contents{flex-grow:1;font-family:Roboto,arial,sans-serif;font-weight:500;overflow:hidden;text-overflow:ellipsis;vertical-align:top}.gsi-material-button .gsi-material-button-state{bottom:0;left:0;opacity:0;position:absolute;right:0;top:0;transition:opacity .218s}.gsi-material-button:disabled{background-color:#ffffff61;cursor:default}.gsi-material-button:disabled .gsi-material-button-state{background-color:#1f1f1f1f}.gsi-material-button:disabled .gsi-material-button-contents,.gsi-material-button:disabled .gsi-material-button-icon{opacity:38%}.gsi-material-button:not(:disabled):active .gsi-material-button-state,.gsi-material-button:not(:disabled):focus .gsi-material-button-state{background-color:#001d35;opacity:12%}.gsi-material-button:not(:disabled):hover{box-shadow:0 1px 2px 0 rgba(60,64,67,.3),0 1px 3px 1px rgba(60,64,67,.15)}.gsi-material-button:not(:disabled):hover .gsi-material-button-state{background-color:#001d35;opacity:8%}.user_warning{font-style:italic;font-weight:700}.sign-link{display:inline-block;text-decoration:none}.signin-container .buttons{opacity:.4;pointer-events:none}.signin-container:has(#terms-of-service-checkbox:checked) .buttons{opacity:1;pointer-events:unset}.signin-container .redirect,.signin-container form{display:flex;flex-direction:column;gap:var(--p-4)}.c-checkbox{cursor:pointer;display:inline-block;position:relative}.c-checkbox input{opacity:0;position:absolute;z-index:-1}.c-checkbox--mark{border-color:var(--augment-green);border-radius:2px;border-style:solid;box-sizing:border-box;display:inline-block;height:20px;margin-right:var(--p-2);vertical-align:bottom;width:20px}.c-checkbox--mark:after{border:solid var(--text-white-80);border-width:0 2px 2px 0;content:"";display:none;height:8px;left:8px;position:absolute;top:4px;transform:rotate(45deg);width:3px}.c-checkbox input:checked~.c-checkbox--mark{background:var(--augment-green)}.c-checkbox input:checked~.c-checkbox--mark:after{display:block}.c-checkbox input:focus~.c-checkbox--mark{border-color:var(--augment-green-dark)}
      /* Springy rocking animation (slight overshoot & damped) */
      @keyframes auggie-rock-spring {
        0% { transform: rotate(0deg); }
        12% { transform: rotate(7deg); }
        24% { transform: rotate(-6deg); }
        36% { transform: rotate(5deg); }
        48% { transform: rotate(-4deg); }
        60% { transform: rotate(3deg); }
        72% { transform: rotate(-2deg); }
        84% { transform: rotate(1deg); }
        92% { transform: rotate(-0.6deg); }
        100% { transform: rotate(0deg); }
      }
      .auggie-logo--rock {
        animation: auggie-rock-spring 4.6s cubic-bezier(.68,-0.4,.32,1.25) infinite;
        transform-origin: 50% 62%; /* lower pivot feels more natural for head */
        will-change: transform;
      }
      @media (prefers-reduced-motion: reduce) {
        .auggie-logo--rock { animation: none; transform: none; }
      }
    </style>
    <script type="text/javascript" async="" charset="utf-8" src="https://www.gstatic.com/recaptcha/releases/_mscDd1KHr60EWWbt2I_ULP0/recaptcha__en.js" crossorigin="anonymous" integrity="sha384-LpPE17frK5ghpDNcL02QVoY7N6n375lMdZEBjkcA4rqimdzMm/XNiOx2MI2jTEkt"></script><script>
      function getAction() {
        // This must return 'signup' to pass reCAPTCHA checks
        return "signup";
      }

      // Auto-submit logic that waits for anti-abuse signal collection
      window.addEventListener("load", () => {
        if (!document.body.getAttribute("data-hv-submitted")) {
          document.body.setAttribute("data-hv-submitted", "true");

          // Use requestAnimationFrame to ensure DOM is fully ready
          requestAnimationFrame(async () => {
            try {
              if (typeof onClick === "function") {
                const fakeEvent = {
                  preventDefault: () => {},
                  target: document.getElementById("action-form"),
                };

                // Wait for the onClick function to complete all signal collection
                await onClick(fakeEvent);
              } else {
                // Fallback: submit form directly if onClick is not available
                console.log(
                  "onClick function not available, submitting form directly",
                );
                const form = document.getElementById("action-form");
                if (form) form.submit();
              }
            } catch (error) {
              console.log("HV auto-submit failed", error);
              const form = document.getElementById("action-form");
              if (form) form.submit();
            }
          });
        }
      });
    </script>
    
<script async="" src="https://js-232.augmentcode.com/prod/bundle.js" verisoul-project-id="9d08ce27-3d9b-4737-b477-e57c7446255b"></script>
<script>!function(e){if(e.Verisoul)return;const r=[],t={},o=new Proxy(t,{get:(e,o)=>o in t?t[o]:(...e)=>new Promise(((t,n)=>r.push([o,e,t,n]))),set:(e,r,o)=>(t[r]=o,!0)});e.Verisoul=o;const n=()=>{Object.keys(t).length&&r.splice(0).forEach((([e,r,o,n])=>{try{Promise.resolve(t[e](...r)).then(o,n)}catch(e){n(e)}}))},c=document.querySelector("script[verisoul-project-id]"),s=()=>r.splice(0).forEach((([,,,e])=>e(new Error("Failed to load Verisoul SDK"))));if(!c)return void s();c.addEventListener("load",n,{once:!0}),c.addEventListener("error",(()=>{clearInterval(i),s()}),{once:!0});const i=setInterval((()=>{Object.keys(t).length&&(clearInterval(i),n())}),40)}(window);</script>


<script src="https://www.google.com/recaptcha/enterprise.js?render=6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1"></script>



<script>
  function withTimeout(promise, timeoutMs) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => reject(new Error("Execution aborted due to timeout.")), timeoutMs);
      promise.finally(() => clearTimeout(timer)).then(resolve, reject);
    });
  }

  async function onClick(e) {
    e.preventDefault();

    
    const verisoulPromise = withTimeout(window.Verisoul.session(), 10000);
    
    
    const grecaptchaPromise = withTimeout((async () => {
      await new Promise((resolve) => {
        grecaptcha.enterprise.ready(resolve);
      });
      return await grecaptcha.enterprise.execute('6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1', { action: getAction() });
    })(), 5000);
    
    
    const verosintPromise = Promise.resolve('');
    

    const errors = [];

    const [verisoulResponse, grecaptchaResponse, verosintResponse] = await Promise.allSettled([verisoulPromise, grecaptchaPromise, verosintPromise]);
    if (verisoulResponse.status === 'fulfilled') {
      document.getElementById('verisoul-session-id').value = verisoulResponse.value.session_id;
    } else {
      console.log('Failed to get verisoul session', verisoulResponse.reason);
      errors.push(`verisoul: ${verisoulResponse.reason.message}`);
    }
    if (grecaptchaResponse.status === 'fulfilled') {
      document.getElementById('g-recaptcha-response').value = grecaptchaResponse.value;
    } else {
      console.log('Failed to get grecaptcha response', grecaptchaResponse.reason);
      errors.push(`recaptcha: ${grecaptchaResponse.reason.message}`);
    }
    if (verosintResponse.status === 'fulfilled') {
      document.getElementById('verosint-deviceid').value = verosintResponse.value;
    } else {
      console.log('Failed to get verosint device id', verosintResponse.reason);
      errors.push(`verosint: ${verosintResponse.reason.message}`);
    }

    // Upload client-side errors to help distinguish real users from bots
    document.getElementById('client-errors').value = JSON.stringify(errors);
    document.getElementById('action-form').submit();
  }

  
  if (typeof window['grecaptcha'] === 'undefined') {
      grecaptcha = {
        ready: function(cb) {
          const c = '___grecaptcha_cfg';
          window[c] = window[c] || {};
          (window[c]['fns'] = window[c]['fns'] || []).push(cb);
        }
      };
  }
  

  
</script>
  </head>
  <body>
    <div class="l-oauth">
  <div class="l-oauth__center">
    <div class="signin-container l-oauth__content">
      <header class="c-header" style="margin-bottom: 24px; display: flex; justify-content: center">
        <svg class="auggie-logo auggie-logo--rock" width="80" height="70" viewBox="0 0 107 94" fill="none" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Augment logo">
          <g clip-path="url(#clip0_350_2033)">
            <path d="M16.4359 93.4605C14.6745 93.4605 13.1907 93.1546 11.9843 92.543C10.7837 91.9313 9.87181 91.0025 9.26015 89.7735C8.64848 88.5445 8.33698 87.0436 8.33698 85.2766V67.0795C8.33698 64.8367 7.89523 63.2056 7.01737 62.1862C6.13385 61.1724 4.5707 60.6343 2.32793 60.5834C1.63131 60.5834 1.07061 60.3172 0.645844 59.7792C0.215412 59.2468 0.00585938 58.6294 0.00585938 57.9385C0.00585938 57.1909 0.215412 56.5736 0.645844 56.0922C1.07061 55.6108 1.63697 55.3446 2.32793 55.2936C4.5707 55.237 6.13385 54.7046 7.01737 53.6908C7.90089 52.677 8.33698 51.0686 8.33698 48.8768V30.6797C8.33698 28.0065 9.0336 25.9732 10.4212 24.5857C11.8088 23.1981 13.8137 22.5015 16.4359 22.5015H37.8272C38.6315 22.5015 39.2941 22.745 39.8321 23.2207C40.3645 23.7021 40.6364 24.3195 40.6364 25.0614C40.6364 25.758 40.4098 26.3584 39.9567 26.8681C39.5037 27.3778 38.9033 27.6327 38.1501 27.6327H18.2879C17.1608 27.6327 16.3056 27.9272 15.7223 28.5162C15.1333 29.1052 14.8388 30.0114 14.8388 31.246V49.5281C14.8388 51.1309 14.5159 52.5864 13.876 53.8947C13.236 55.2086 12.3808 56.2337 11.3104 56.987C10.2399 57.7346 8.9883 58.1084 7.54408 58.1084V57.7856C8.9883 57.7856 10.2399 58.1594 11.3104 58.907C12.3808 59.6545 13.236 60.6853 13.876 61.9993C14.5159 63.3076 14.8388 64.7631 14.8388 66.3659V84.7272C14.8388 85.9619 15.1333 86.868 15.7223 87.4571C16.3113 88.0517 17.1665 88.3406 18.2879 88.3406H38.1501C38.8977 88.3406 39.498 88.5954 39.9567 89.1052C40.4155 89.6149 40.6364 90.2152 40.6364 90.9118C40.6364 91.6085 40.3702 92.2031 39.8321 92.7129C39.2941 93.2226 38.6315 93.4774 37.8272 93.4774H16.4359V93.4605Z" fill="#2F92DB"></path>
            <path d="M68.8736 93.4608C68.0694 93.4608 67.4067 93.206 66.8687 92.6963C66.3306 92.1865 66.0645 91.5862 66.0645 90.8952C66.0645 90.2043 66.291 89.5983 66.7441 89.0886C67.1972 88.5788 67.7975 88.324 68.5508 88.324H88.4129C89.54 88.324 90.3952 88.0351 90.9785 87.4405C91.5675 86.8514 91.8621 85.9453 91.8621 84.7106V66.3493C91.8621 64.7465 92.1849 63.291 92.8249 61.9827C93.4648 60.6687 94.32 59.6436 95.3905 58.8904C96.4609 58.1428 97.7125 57.769 99.1567 57.769V58.0918C97.7125 58.0918 96.4609 57.718 95.3905 56.9704C94.32 56.2228 93.4648 55.192 92.8249 53.8781C92.1849 52.5698 91.8621 51.1143 91.8621 49.5115V31.2294C91.8621 30.0004 91.5675 29.0943 90.9785 28.4996C90.3895 27.9106 89.5343 27.6161 88.4129 27.6161H68.5508C67.8032 27.6161 67.2028 27.3612 66.7441 26.8515C66.291 26.3474 66.0645 25.7414 66.0645 25.0448C66.0645 24.2972 66.3306 23.6855 66.8687 23.2041C67.4011 22.7227 68.0694 22.4849 68.8736 22.4849H90.2649C92.8872 22.4849 94.8864 23.1815 96.2796 24.5691C97.6729 25.9566 98.3638 27.9899 98.3638 30.6631V48.8602C98.3638 51.052 98.8056 52.6604 99.6835 53.6742C100.567 54.688 102.13 55.226 104.373 55.277C105.07 55.328 105.63 55.5942 106.055 56.0756C106.485 56.557 106.695 57.1743 106.695 57.9219C106.695 58.6185 106.485 59.2302 106.055 59.7625C105.63 60.2949 105.064 60.5668 104.373 60.5668C102.13 60.6177 100.567 61.1558 99.6835 62.1696C98.7999 63.1833 98.3638 64.8145 98.3638 67.0629V85.26C98.3638 87.027 98.058 88.5222 97.4407 89.7569C96.8234 90.9915 95.9172 91.909 94.7165 92.5264C93.5102 93.138 92.0263 93.4439 90.2649 93.4439H68.8736V93.4608Z" fill="#2F92DB"></path>
            <path d="M74.3713 64.8959C78.267 64.8959 81.4252 61.7377 81.4252 57.842C81.4252 53.9462 78.267 50.7881 74.3713 50.7881C70.4755 50.7881 67.3174 53.9462 67.3174 57.842C67.3174 61.7377 70.4755 64.8959 74.3713 64.8959Z" fill="#2F92DB"></path>
            <path d="M34.8703 64.8959C38.7661 64.8959 41.9242 61.7377 41.9242 57.842C41.9242 53.9462 38.7661 50.7881 34.8703 50.7881C30.9745 50.7881 27.8164 53.9462 27.8164 57.842C27.8164 61.7377 30.9745 64.8959 34.8703 64.8959Z" fill="#2F92DB"></path>
          </g>
          <path d="M50.9486 2.09399L50.4098 16.5074C50.4098 17.3156 49.6465 17.7197 48.1198 17.7197C46.5932 17.7197 45.8298 17.3156 45.8298 16.5074C45.6951 12.9152 45.5829 10.1314 45.4931 8.15569C45.4482 6.13513 45.4033 4.69828 45.3584 3.84515C45.3135 2.99202 45.291 2.47565 45.291 2.29605C45.291 2.11644 45.291 2.02664 45.291 2.02664C45.291 0.993906 46.2339 0.477539 48.1198 0.477539C50.0057 0.477539 50.9486 1.01636 50.9486 2.09399ZM62.3985 2.09399L61.8597 16.5074C61.8597 17.3156 61.0963 17.7197 59.5697 17.7197C58.043 17.7197 57.2797 17.3156 57.2797 16.5074C57.145 12.9152 57.0328 10.1314 56.9429 8.15569C56.898 6.13513 56.8531 4.69828 56.8082 3.84515C56.7633 2.99202 56.7409 2.47565 56.7409 2.29605C56.7409 2.11644 56.7409 2.02664 56.7409 2.02664C56.7409 0.993906 57.6838 0.477539 59.5697 0.477539C61.4556 0.477539 62.3985 1.01636 62.3985 2.09399Z" fill="#2F92DB"></path>
          <defs>
            <clipPath id="clip0_350_2033">
              <rect width="106.702" height="93.4604" fill="white"></rect>
            </clipPath>
          </defs>
        </svg>
      </header>
      <div class="u-text-sm" id="verification-message">
        Verifying you are human...
      </div>
      <form method="post" action="/terms-accept?response_type=code&amp;client_id=v&amp;code_challenge=9-c-YQ_x0v7R9kccVNOC4WfTF-VXX5G6dPixA3U5mFI&amp;code_challenge_method=S256&amp;state=q1ElNrlkzeR4C3ZoKEWVtg&amp;prompt=login" id="action-form">
        <input type="hidden" name="continue" value="continue">
        <input type="hidden" name="terms-of-service" value="accepted">
        
        <input type="hidden" name="sign_up" value="true">
        
        <input type="hidden" name="g-recaptcha-response" id="g-recaptcha-response">
        <input type="hidden" name="verisoul-session-id" id="verisoul-session-id">
        <input type="hidden" name="verosint-deviceid" id="verosint-deviceid">
        <input type="hidden" name="client-errors" id="client-errors">
        
      </form>
    </div>
      </div>
  <footer class="c-footer">
  <p>
    © 2025 Augment Computing, Inc.
  </p>
  <div class="c-footer__links">
    <a href="mailto:<EMAIL>">Contact Us</a>
    <a href="/privacy-policy">Privacy Policy</a>
  </div>
</footer>
</div>
  
<div><div class="grecaptcha-badge" data-style="bottomright" style="width: 256px; height: 60px; display: block; transition: right 0.3s; position: fixed; bottom: 14px; right: -186px; box-shadow: gray 0px 0px 5px; border-radius: 2px; overflow: hidden;"><div class="grecaptcha-logo"><iframe title="reCAPTCHA" width="256" height="60" role="presentation" name="a-gt5dmmgxm2gz" frameborder="0" scrolling="no" sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation" src="https://www.google.com/recaptcha/enterprise/anchor?ar=1&amp;k=6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1&amp;co=aHR0cHM6Ly9hdXRoLmF1Z21lbnRjb2RlLmNvbTo0NDM.&amp;hl=en&amp;v=_mscDd1KHr60EWWbt2I_ULP0&amp;size=invisible&amp;anchor-ms=20000&amp;execute-ms=15000&amp;cb=6u4tr4bbow56"></iframe></div><div class="grecaptcha-error"></div><textarea id="g-recaptcha-response-100000" name="g-recaptcha-response" class="g-recaptcha-response" style="width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"></textarea></div></div></body></html>